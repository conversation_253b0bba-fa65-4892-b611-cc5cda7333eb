#include "ti_msp_dl_config.h"
#include "clock.h"

// 统一的系统时间变量，与调度器共享
volatile uint32_t sys_tick = 0;
volatile uint32_t start_time;

int mspm0_delay_ms(unsigned long num_ms)
{
    start_time = sys_tick;
    while (sys_tick - start_time < num_ms);
    return 0;
}

int mspm0_get_clock_ms(unsigned long *count)
{
    if (!count)
        return 1;
    count[0] = sys_tick;
    return 0;
}

// 延时函数（毫秒）
void SystemTime_DelayMs(uint32_t ms)
{
    uint32_t start = sys_tick;
    while (sys_tick - start < ms);
}

// 获取当前时间（毫秒）
uint32_t SystemTime_GetMs(void)
{
    return sys_tick;
}

// SysTick初始化函数（由调度器调用）
void SysTick_Init(void)
{
    // 配置SysTick定时器，每1ms产生一次中断
    // 使用CPUCLK_FREQ定义的系统时钟频率
    // CPUCLK_FREQ = 32MHz，1ms需要32000个时钟周期
    DL_SYSTICK_config(CPUCLK_FREQ / 1000 - 1);

    // 启用SysTick中断
    DL_SYSTICK_enableInterrupt();

    // 启动SysTick定时器
    DL_SYSTICK_enable();
}
