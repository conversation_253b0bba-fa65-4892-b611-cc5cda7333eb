<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o empty.out -mempty.map -iD:/TI/CCS/mspm0_sdk_2_05_00_05/source -iD:/Projects/TI/empty -iD:/Projects/TI/empty/Debug/syscfg -iD:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=empty_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./scheduler.o ./APP/oled_app.o ./BSP/oled_hardware_i2c.o ./System/clock.o ./System/interrupt.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x685bf40a</link_time>
   <link_errors>0x0</link_errors>
   <output_file>D:\Projects\TI\empty\Debug\empty.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x9b1</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>scheduler.o</file>
         <name>scheduler.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>D:\Projects\TI\empty\Debug\.\APP\</path>
         <kind>object</kind>
         <file>oled_app.o</file>
         <name>oled_app.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>D:\Projects\TI\empty\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>oled_hardware_i2c.o</file>
         <name>oled_hardware_i2c.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>D:\Projects\TI\empty\Debug\.\System\</path>
         <kind>object</kind>
         <file>clock.o</file>
         <name>clock.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>D:\Projects\TI\empty\Debug\.\System\</path>
         <kind>object</kind>
         <file>interrupt.o</file>
         <name>interrupt.o</name>
      </input_file>
      <input_file id="fl-15">
         <path>D:\Projects\TI\empty\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-16">
         <path>D:\TI\CCS\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>D:\TI\CCS\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_i2c.o</name>
      </input_file>
      <input_file id="fl-2e">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-da">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-db">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-dc">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-dd">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-de">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-90">
         <name>.text.OLED_Init</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x2c0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.text.OLED_WR_Byte</name>
         <load_address>0x380</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x380</run_address>
         <size>0x134</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-108">
         <name>.text.OLED_ShowChar</name>
         <load_address>0x4b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4b4</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-86">
         <name>.text:memcpy</name>
         <load_address>0x590</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x590</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x62a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x62a</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x62c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x62c</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-104">
         <name>.text.OLED_ShowString</name>
         <load_address>0x6a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6a8</run_address>
         <size>0x7a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.text.DL_I2C_fillControllerTXFIFO</name>
         <load_address>0x722</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x722</run_address>
         <size>0x5e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.text.SYSCFG_DL_I2C_OLED_init</name>
         <load_address>0x780</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x780</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.text.scheduler_run</name>
         <load_address>0x7d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7d4</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x818</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x818</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.text.SysTick_Init</name>
         <load_address>0x858</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x858</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-109">
         <name>.text.__aeabi_uidivmod</name>
         <load_address>0x898</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x898</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x8d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8d8</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x914</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x914</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x948</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x948</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.text.mspm0_delay_ms</name>
         <load_address>0x97c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x97c</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-55">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x9b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9b0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.text.DL_I2C_setClockConfig</name>
         <load_address>0x9d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9d8</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.text.main</name>
         <load_address>0x9fe</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9fe</run_address>
         <size>0x1a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.text.mspm0_get_clock_ms</name>
         <load_address>0xa18</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa18</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0xa30</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa30</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0xa46</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa46</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0xa5a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa5a</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.text.oled_task</name>
         <load_address>0xa5c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa5c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-96">
         <name>.text.scheduler_init</name>
         <load_address>0xa70</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa70</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0xa84</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa84</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.SysTick_Handler</name>
         <load_address>0xa98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa98</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0xaa8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xaa8</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.text.led_blink_task</name>
         <load_address>0xab4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xab4</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0xac0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xac0</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-45">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0xacc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xacc</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.text:abort</name>
         <load_address>0xad4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xad4</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.text.HOSTexit</name>
         <load_address>0xada</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xada</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0xade</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xade</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-69">
         <name>.text._system_pre_init</name>
         <load_address>0xae2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xae2</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-159">
         <name>.cinit..data.load</name>
         <load_address>0x1308</load_address>
         <readonly>true</readonly>
         <run_address>0x1308</run_address>
         <size>0x16</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-157">
         <name>__TI_handler_table</name>
         <load_address>0x1320</load_address>
         <readonly>true</readonly>
         <run_address>0x1320</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-15a">
         <name>.cinit..bss.load</name>
         <load_address>0x132c</load_address>
         <readonly>true</readonly>
         <run_address>0x132c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-158">
         <name>__TI_cinit_table</name>
         <load_address>0x1334</load_address>
         <readonly>true</readonly>
         <run_address>0x1334</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-10d">
         <name>.rodata.asc2_1608</name>
         <load_address>0xae8</load_address>
         <readonly>true</readonly>
         <run_address>0xae8</run_address>
         <size>0x5f0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.rodata.asc2_0806</name>
         <load_address>0x10d8</load_address>
         <readonly>true</readonly>
         <run_address>0x10d8</run_address>
         <size>0x228</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-105">
         <name>.rodata.str1.17288060079586621852.1</name>
         <load_address>0x1300</load_address>
         <readonly>true</readonly>
         <run_address>0x1300</run_address>
         <size>0x6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.rodata.gI2C_OLEDClockConfig</name>
         <load_address>0x1306</load_address>
         <readonly>true</readonly>
         <run_address>0x1306</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-121">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-cf">
         <name>.data.scheduler_task</name>
         <load_address>0x20200000</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.data.sys_tick</name>
         <load_address>0x20200018</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200018</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.common:task_num</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200020</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-ec">
         <name>.common:start_time</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020001c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x10d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_abbrev</name>
         <load_address>0x10d</load_address>
         <run_address>0x10d</run_address>
         <size>0x1b8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_abbrev</name>
         <load_address>0x2c5</load_address>
         <run_address>0x2c5</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_abbrev</name>
         <load_address>0x332</load_address>
         <run_address>0x332</run_address>
         <size>0xdc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-106">
         <name>.debug_abbrev</name>
         <load_address>0x40e</load_address>
         <run_address>0x40e</run_address>
         <size>0x81</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_abbrev</name>
         <load_address>0x48f</load_address>
         <run_address>0x48f</run_address>
         <size>0x294</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_abbrev</name>
         <load_address>0x723</load_address>
         <run_address>0x723</run_address>
         <size>0x1b1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_abbrev</name>
         <load_address>0x8d4</load_address>
         <run_address>0x8d4</run_address>
         <size>0x115</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.debug_abbrev</name>
         <load_address>0x9e9</load_address>
         <run_address>0x9e9</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-102">
         <name>.debug_abbrev</name>
         <load_address>0xa4b</load_address>
         <run_address>0xa4b</run_address>
         <size>0x1e7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_abbrev</name>
         <load_address>0xc32</load_address>
         <run_address>0xc32</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_abbrev</name>
         <load_address>0xce1</load_address>
         <run_address>0xce1</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_abbrev</name>
         <load_address>0xe51</load_address>
         <run_address>0xe51</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_abbrev</name>
         <load_address>0xe8a</load_address>
         <run_address>0xe8a</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0xf4c</load_address>
         <run_address>0xf4c</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_abbrev</name>
         <load_address>0xfbc</load_address>
         <run_address>0xfbc</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.debug_abbrev</name>
         <load_address>0x1049</load_address>
         <run_address>0x1049</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.debug_abbrev</name>
         <load_address>0x10e1</load_address>
         <run_address>0x10e1</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_abbrev</name>
         <load_address>0x110d</load_address>
         <run_address>0x110d</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-114">
         <name>.debug_abbrev</name>
         <load_address>0x1134</load_address>
         <run_address>0x1134</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-115">
         <name>.debug_abbrev</name>
         <load_address>0x115b</load_address>
         <run_address>0x115b</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_abbrev</name>
         <load_address>0x11b4</load_address>
         <run_address>0x11b4</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.debug_abbrev</name>
         <load_address>0x11d9</load_address>
         <run_address>0x11d9</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x759</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_info</name>
         <load_address>0x759</load_address>
         <run_address>0x759</run_address>
         <size>0x1c68</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x23c1</load_address>
         <run_address>0x23c1</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_info</name>
         <load_address>0x2441</load_address>
         <run_address>0x2441</run_address>
         <size>0x13f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_info</name>
         <load_address>0x2580</load_address>
         <run_address>0x2580</run_address>
         <size>0x8f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_info</name>
         <load_address>0x260f</load_address>
         <run_address>0x260f</run_address>
         <size>0x25f0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_info</name>
         <load_address>0x4bff</load_address>
         <run_address>0x4bff</run_address>
         <size>0x4af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_info</name>
         <load_address>0x50ae</load_address>
         <run_address>0x50ae</run_address>
         <size>0x229</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.debug_info</name>
         <load_address>0x52d7</load_address>
         <run_address>0x52d7</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_info</name>
         <load_address>0x534c</load_address>
         <run_address>0x534c</run_address>
         <size>0xcc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x600e</load_address>
         <run_address>0x600e</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_info</name>
         <load_address>0x6431</load_address>
         <run_address>0x6431</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_info</name>
         <load_address>0x6b75</load_address>
         <run_address>0x6b75</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_info</name>
         <load_address>0x6bbb</load_address>
         <run_address>0x6bbb</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_info</name>
         <load_address>0x6d4d</load_address>
         <run_address>0x6d4d</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_info</name>
         <load_address>0x6e13</load_address>
         <run_address>0x6e13</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_info</name>
         <load_address>0x6f8f</load_address>
         <run_address>0x6f8f</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_info</name>
         <load_address>0x7087</load_address>
         <run_address>0x7087</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_info</name>
         <load_address>0x70c2</load_address>
         <run_address>0x70c2</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.debug_info</name>
         <load_address>0x725b</load_address>
         <run_address>0x725b</run_address>
         <size>0x1bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-110">
         <name>.debug_info</name>
         <load_address>0x7417</load_address>
         <run_address>0x7417</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_info</name>
         <load_address>0x749c</load_address>
         <run_address>0x749c</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.debug_info</name>
         <load_address>0x7796</load_address>
         <run_address>0x7796</run_address>
         <size>0x8e</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_ranges</name>
         <load_address>0x18</load_address>
         <run_address>0x18</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_ranges</name>
         <load_address>0x48</load_address>
         <run_address>0x48</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_ranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_ranges</name>
         <load_address>0x78</load_address>
         <run_address>0x78</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_ranges</name>
         <load_address>0x1e8</load_address>
         <run_address>0x1e8</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_ranges</name>
         <load_address>0x218</load_address>
         <run_address>0x218</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_ranges</name>
         <load_address>0x230</load_address>
         <run_address>0x230</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_ranges</name>
         <load_address>0x408</load_address>
         <run_address>0x408</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_ranges</name>
         <load_address>0x450</load_address>
         <run_address>0x450</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_ranges</name>
         <load_address>0x498</load_address>
         <run_address>0x498</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_ranges</name>
         <load_address>0x4b0</load_address>
         <run_address>0x4b0</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_ranges</name>
         <load_address>0x500</load_address>
         <run_address>0x500</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-113">
         <name>.debug_ranges</name>
         <load_address>0x518</load_address>
         <run_address>0x518</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_ranges</name>
         <load_address>0x530</load_address>
         <run_address>0x530</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x476</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_str</name>
         <load_address>0x476</load_address>
         <run_address>0x476</run_address>
         <size>0x170a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_str</name>
         <load_address>0x1b80</load_address>
         <run_address>0x1b80</run_address>
         <size>0x149</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_str</name>
         <load_address>0x1cc9</load_address>
         <run_address>0x1cc9</run_address>
         <size>0x166</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-107">
         <name>.debug_str</name>
         <load_address>0x1e2f</load_address>
         <run_address>0x1e2f</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_str</name>
         <load_address>0x1f27</load_address>
         <run_address>0x1f27</run_address>
         <size>0xf50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_str</name>
         <load_address>0x2e77</load_address>
         <run_address>0x2e77</run_address>
         <size>0x4ca</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_str</name>
         <load_address>0x3341</load_address>
         <run_address>0x3341</run_address>
         <size>0x209</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_str</name>
         <load_address>0x354a</load_address>
         <run_address>0x354a</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-103">
         <name>.debug_str</name>
         <load_address>0x36b7</load_address>
         <run_address>0x36b7</run_address>
         <size>0x8af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_str</name>
         <load_address>0x3f66</load_address>
         <run_address>0x3f66</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_str</name>
         <load_address>0x418b</load_address>
         <run_address>0x418b</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_str</name>
         <load_address>0x44ba</load_address>
         <run_address>0x44ba</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_str</name>
         <load_address>0x45af</load_address>
         <run_address>0x45af</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_str</name>
         <load_address>0x474a</load_address>
         <run_address>0x474a</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_str</name>
         <load_address>0x48b2</load_address>
         <run_address>0x48b2</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_str</name>
         <load_address>0x4a87</load_address>
         <run_address>0x4a87</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.debug_str</name>
         <load_address>0x4bcf</load_address>
         <run_address>0x4bcf</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-116">
         <name>.debug_str</name>
         <load_address>0x4cb8</load_address>
         <run_address>0x4cb8</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_frame</name>
         <load_address>0x30</load_address>
         <run_address>0x30</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_frame</name>
         <load_address>0xa8</load_address>
         <run_address>0xa8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_frame</name>
         <load_address>0xd8</load_address>
         <run_address>0xd8</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_frame</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_frame</name>
         <load_address>0x148</load_address>
         <run_address>0x148</run_address>
         <size>0x1e4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_frame</name>
         <load_address>0x32c</load_address>
         <run_address>0x32c</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_frame</name>
         <load_address>0x394</load_address>
         <run_address>0x394</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_frame</name>
         <load_address>0x3c4</load_address>
         <run_address>0x3c4</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_frame</name>
         <load_address>0x3e4</load_address>
         <run_address>0x3e4</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_frame</name>
         <load_address>0x510</load_address>
         <run_address>0x510</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_frame</name>
         <load_address>0x5a0</load_address>
         <run_address>0x5a0</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_frame</name>
         <load_address>0x6a0</load_address>
         <run_address>0x6a0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_frame</name>
         <load_address>0x6c0</load_address>
         <run_address>0x6c0</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_frame</name>
         <load_address>0x6f8</load_address>
         <run_address>0x6f8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_frame</name>
         <load_address>0x720</load_address>
         <run_address>0x720</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_frame</name>
         <load_address>0x750</load_address>
         <run_address>0x750</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_frame</name>
         <load_address>0x780</load_address>
         <run_address>0x780</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_frame</name>
         <load_address>0x7a0</load_address>
         <run_address>0x7a0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1e9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_line</name>
         <load_address>0x1e9</load_address>
         <run_address>0x1e9</run_address>
         <size>0x44e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_line</name>
         <load_address>0x637</load_address>
         <run_address>0x637</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_line</name>
         <load_address>0x6f3</load_address>
         <run_address>0x6f3</run_address>
         <size>0x147</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_line</name>
         <load_address>0x83a</load_address>
         <run_address>0x83a</run_address>
         <size>0xf0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_line</name>
         <load_address>0x92a</load_address>
         <run_address>0x92a</run_address>
         <size>0xf31</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_line</name>
         <load_address>0x185b</load_address>
         <run_address>0x185b</run_address>
         <size>0x2cc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_line</name>
         <load_address>0x1b27</load_address>
         <run_address>0x1b27</run_address>
         <size>0x201</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_line</name>
         <load_address>0x1d28</load_address>
         <run_address>0x1d28</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.debug_line</name>
         <load_address>0x1ea0</load_address>
         <run_address>0x1ea0</run_address>
         <size>0x682</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_line</name>
         <load_address>0x2522</load_address>
         <run_address>0x2522</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_line</name>
         <load_address>0x26fe</load_address>
         <run_address>0x26fe</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.debug_line</name>
         <load_address>0x2c18</load_address>
         <run_address>0x2c18</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_line</name>
         <load_address>0x2c56</load_address>
         <run_address>0x2c56</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x2d54</load_address>
         <run_address>0x2d54</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_line</name>
         <load_address>0x2e14</load_address>
         <run_address>0x2e14</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_line</name>
         <load_address>0x2fdc</load_address>
         <run_address>0x2fdc</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_line</name>
         <load_address>0x3043</load_address>
         <run_address>0x3043</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_line</name>
         <load_address>0x3084</load_address>
         <run_address>0x3084</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.debug_line</name>
         <load_address>0x3128</load_address>
         <run_address>0x3128</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-112">
         <name>.debug_line</name>
         <load_address>0x31ea</load_address>
         <run_address>0x31ea</run_address>
         <size>0xb5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_line</name>
         <load_address>0x329f</load_address>
         <run_address>0x329f</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x92</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_loc</name>
         <load_address>0x92</load_address>
         <run_address>0x92</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_loc</name>
         <load_address>0xd3</load_address>
         <run_address>0xd3</run_address>
         <size>0xf80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_loc</name>
         <load_address>0x1053</load_address>
         <run_address>0x1053</run_address>
         <size>0x83</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_loc</name>
         <load_address>0x10d6</load_address>
         <run_address>0x10d6</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.debug_loc</name>
         <load_address>0x10e9</load_address>
         <run_address>0x10e9</run_address>
         <size>0x352</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_loc</name>
         <load_address>0x143b</load_address>
         <run_address>0x143b</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_loc</name>
         <load_address>0x1513</load_address>
         <run_address>0x1513</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_loc</name>
         <load_address>0x1937</load_address>
         <run_address>0x1937</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_loc</name>
         <load_address>0x1aa3</load_address>
         <run_address>0x1aa3</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_loc</name>
         <load_address>0x1b12</load_address>
         <run_address>0x1b12</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_loc</name>
         <load_address>0x1c79</load_address>
         <run_address>0x1c79</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-117">
         <name>.debug_loc</name>
         <load_address>0x1c9f</load_address>
         <run_address>0x1c9f</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0xa28</size>
         <contents>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-69"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x1308</load_address>
         <run_address>0x1308</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-157"/>
            <object_component_ref idref="oc-15a"/>
            <object_component_ref idref="oc-158"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0xae8</load_address>
         <run_address>0xae8</run_address>
         <size>0x820</size>
         <contents>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-eb"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-121"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200000</run_address>
         <size>0x1c</size>
         <contents>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-5c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x2020001c</run_address>
         <size>0x5</size>
         <contents>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-ec"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-15c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-118" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-119" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11a" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11b" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11c" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11d" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11f" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13b" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x11e8</size>
         <contents>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-15e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-13d" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7824</size>
         <contents>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-15d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-13f" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x558</size>
         <contents>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-83"/>
         </contents>
      </logical_group>
      <logical_group id="lg-141" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4e4b</size>
         <contents>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-116"/>
         </contents>
      </logical_group>
      <logical_group id="lg-143" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7d0</size>
         <contents>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-111"/>
         </contents>
      </logical_group>
      <logical_group id="lg-145" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x333f</size>
         <contents>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-84"/>
         </contents>
      </logical_group>
      <logical_group id="lg-147" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1cbf</size>
         <contents>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-117"/>
         </contents>
      </logical_group>
      <logical_group id="lg-151" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x68</size>
         <contents>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-82"/>
         </contents>
      </logical_group>
      <logical_group id="lg-15b" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-164" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1348</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-165" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x21</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-e"/>
            <logical_group_ref idref="lg-f"/>
         </contents>
      </load_segment>
      <load_segment id="lg-166" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x1348</used_space>
         <unused_space>0x1ecb8</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0xa28</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xae8</start_address>
               <size>0x820</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x1308</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x1348</start_address>
               <size>0x1ecb8</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x221</used_space>
         <unused_space>0x7ddf</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-11d"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-11f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x1c</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x2020001c</start_address>
               <size>0x5</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200021</start_address>
               <size>0x7ddf</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x1308</load_address>
            <load_size>0x16</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x1c</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x132c</load_address>
            <load_size>0x8</load_size>
            <run_address>0x2020001c</run_address>
            <run_size>0x5</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x1334</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x1344</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x1344</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x1320</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x132c</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3e">
         <name>led_blink_task</name>
         <value>0xab5</value>
         <object_component_ref idref="oc-ef"/>
      </symbol>
      <symbol id="sm-3f">
         <name>main</name>
         <value>0x9ff</value>
         <object_component_ref idref="oc-6d"/>
      </symbol>
      <symbol id="sm-57">
         <name>SYSCFG_DL_init</name>
         <value>0xa47</value>
         <object_component_ref idref="oc-8b"/>
      </symbol>
      <symbol id="sm-58">
         <name>SYSCFG_DL_initPower</name>
         <value>0x949</value>
         <object_component_ref idref="oc-bf"/>
      </symbol>
      <symbol id="sm-59">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x915</value>
         <object_component_ref idref="oc-c0"/>
      </symbol>
      <symbol id="sm-5a">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x819</value>
         <object_component_ref idref="oc-c1"/>
      </symbol>
      <symbol id="sm-5b">
         <name>SYSCFG_DL_I2C_OLED_init</name>
         <value>0x781</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-66">
         <name>Default_Handler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-67">
         <name>Reset_Handler</name>
         <value>0xadf</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-68">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-69">
         <name>NMI_Handler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6a">
         <name>HardFault_Handler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6b">
         <name>SVC_Handler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6c">
         <name>PendSV_Handler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6d">
         <name>GROUP0_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6e">
         <name>TIMG8_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6f">
         <name>UART3_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-70">
         <name>ADC0_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-71">
         <name>ADC1_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-72">
         <name>CANFD0_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-73">
         <name>DAC0_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-74">
         <name>SPI0_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-75">
         <name>SPI1_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-76">
         <name>UART1_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-77">
         <name>UART2_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-78">
         <name>UART0_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-79">
         <name>TIMG0_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7a">
         <name>TIMG6_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7b">
         <name>TIMA0_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7c">
         <name>TIMA1_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7d">
         <name>TIMG7_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7e">
         <name>TIMG12_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7f">
         <name>I2C0_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-80">
         <name>I2C1_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-81">
         <name>AES_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-82">
         <name>RTC_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-83">
         <name>DMA_IRQHandler</name>
         <value>0x62b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-93">
         <name>scheduler_init</name>
         <value>0xa71</value>
         <object_component_ref idref="oc-96"/>
      </symbol>
      <symbol id="sm-94">
         <name>task_num</name>
         <value>0x20200020</value>
      </symbol>
      <symbol id="sm-95">
         <name>scheduler_run</name>
         <value>0x7d5</value>
         <object_component_ref idref="oc-9b"/>
      </symbol>
      <symbol id="sm-9f">
         <name>oled_task</name>
         <value>0xa5d</value>
         <object_component_ref idref="oc-f0"/>
      </symbol>
      <symbol id="sm-b2">
         <name>OLED_WR_Byte</name>
         <value>0x381</value>
         <object_component_ref idref="oc-c8"/>
      </symbol>
      <symbol id="sm-b3">
         <name>OLED_ShowChar</name>
         <value>0x4b5</value>
         <object_component_ref idref="oc-108"/>
      </symbol>
      <symbol id="sm-b4">
         <name>asc2_1608</name>
         <value>0xae8</value>
         <object_component_ref idref="oc-10d"/>
      </symbol>
      <symbol id="sm-b5">
         <name>asc2_0806</name>
         <value>0x10d8</value>
         <object_component_ref idref="oc-10e"/>
      </symbol>
      <symbol id="sm-b6">
         <name>OLED_ShowString</name>
         <value>0x6a9</value>
         <object_component_ref idref="oc-104"/>
      </symbol>
      <symbol id="sm-b7">
         <name>OLED_Init</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-90"/>
      </symbol>
      <symbol id="sm-c8">
         <name>mspm0_delay_ms</name>
         <value>0x97d</value>
         <object_component_ref idref="oc-c6"/>
      </symbol>
      <symbol id="sm-c9">
         <name>sys_tick</name>
         <value>0x20200018</value>
         <object_component_ref idref="oc-5c"/>
      </symbol>
      <symbol id="sm-ca">
         <name>start_time</name>
         <value>0x2020001c</value>
      </symbol>
      <symbol id="sm-cb">
         <name>mspm0_get_clock_ms</name>
         <value>0xa19</value>
         <object_component_ref idref="oc-ed"/>
      </symbol>
      <symbol id="sm-cc">
         <name>SysTick_Init</name>
         <value>0x859</value>
         <object_component_ref idref="oc-cb"/>
      </symbol>
      <symbol id="sm-d9">
         <name>SysTick_Handler</name>
         <value>0xa99</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-da">
         <name>GROUP1_IRQHandler</name>
         <value>0xaa9</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-db">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-dc">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-dd">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-de">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-df">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-e0">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-e1">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-e2">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-e3">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ec">
         <name>DL_Common_delayCycles</name>
         <value>0xac1</value>
         <object_component_ref idref="oc-e1"/>
      </symbol>
      <symbol id="sm-f8">
         <name>DL_I2C_setClockConfig</name>
         <value>0x9d9</value>
         <object_component_ref idref="oc-e5"/>
      </symbol>
      <symbol id="sm-f9">
         <name>DL_I2C_fillControllerTXFIFO</name>
         <value>0x723</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-104">
         <name>_c_int00_noargs</name>
         <value>0x9b1</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-105">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-111">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x8d9</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-119">
         <name>_system_pre_init</name>
         <value>0xae3</value>
         <object_component_ref idref="oc-69"/>
      </symbol>
      <symbol id="sm-124">
         <name>__TI_zero_init_nomemset</name>
         <value>0xa31</value>
         <object_component_ref idref="oc-4c"/>
      </symbol>
      <symbol id="sm-12d">
         <name>__TI_decompress_none</name>
         <value>0xa85</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-138">
         <name>__TI_decompress_lzss</name>
         <value>0x62d</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-142">
         <name>abort</name>
         <value>0xad5</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-14c">
         <name>HOSTexit</name>
         <value>0xadb</value>
         <object_component_ref idref="oc-d0"/>
      </symbol>
      <symbol id="sm-14d">
         <name>C$$EXIT</name>
         <value>0xada</value>
         <object_component_ref idref="oc-d0"/>
      </symbol>
      <symbol id="sm-153">
         <name>__aeabi_memcpy</name>
         <value>0xacd</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-154">
         <name>__aeabi_memcpy4</name>
         <value>0xacd</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-155">
         <name>__aeabi_memcpy8</name>
         <value>0xacd</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-15b">
         <name>__aeabi_uidiv</name>
         <value>0x899</value>
         <object_component_ref idref="oc-109"/>
      </symbol>
      <symbol id="sm-15c">
         <name>__aeabi_uidivmod</name>
         <value>0x899</value>
         <object_component_ref idref="oc-109"/>
      </symbol>
      <symbol id="sm-166">
         <name>__aeabi_idiv0</name>
         <value>0xa5b</value>
         <object_component_ref idref="oc-10f"/>
      </symbol>
      <symbol id="sm-17f">
         <name>memcpy</name>
         <value>0x591</value>
         <object_component_ref idref="oc-86"/>
      </symbol>
      <symbol id="sm-180">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-183">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-184">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
