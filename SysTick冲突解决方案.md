# SysTick冲突解决方案

## 问题描述
OLED底层驱动与任务调度器都使用了SysTick定时器，导致冲突。需要将System文件夹中的SysTick替换为TIMER_1ms定时器，以调度器为准。

## 解决方案概述
将System文件夹中的SysTick实现替换为使用TIMER_1ms定时器，保持OLED驱动的时间基准功能，同时避免与调度器的SysTick冲突。

## 修改详情

### 1. 修改 System/clock.c
**原始问题**: 使用SysTick_Init()和DL_SYSTICK_config()
**解决方案**: 替换为TIMER_1ms定时器

```c
// 新增函数：使用TIMER_1ms替代SysTick初始化
void Timer_1ms_Init(void)
{
    // TIMER_1ms已经在ti_msp_dl_config.c中配置好了
    // 这里只需要启用中断
    NVIC_SetPriority(TIMER_1ms_INST_INT_IRQN, 0);
    NVIC_EnableIRQ(TIMER_1ms_INST_INT_IRQN);
}

// 保持兼容性的函数名
void SysTick_Init(void)
{
    Timer_1ms_Init();
}
```

### 2. 修改 System/interrupt.c
**原始问题**: SysTick_Handler()与调度器冲突
**解决方案**: 替换为TIMER_1ms中断处理函数

```c
// TIMER_1ms中断处理函数，替代SysTick_Handler
void TIMER_1ms_INST_IRQHandler(void)
{
    switch (DL_TimerG_getPendingInterrupt(TIMER_1ms_INST)) {
        case DL_TIMERG_IIDX_ZERO:
            tick_ms++;
            break;
        default:
            break;
    }
}
```

### 3. 修改 System/clock.h
**新增**: Timer_1ms_Init()函数声明

```c
void Timer_1ms_Init(void);  // 新的定时器初始化函数
void SysTick_Init(void);    // 保持兼容性
```

### 4. 修改 scheduler.c
**原始问题**: 使用自己的sys_tick变量和SysTick配置
**解决方案**: 使用System文件夹中的tick_ms变量

```c
#include "System/clock.h"

// 使用System文件夹中的tick_ms作为时基
extern volatile unsigned long tick_ms;

void scheduler_init(void)
{
    // 初始化1ms定时器（替代SysTick）
    Timer_1ms_Init();
    
    // 计算任务数量
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

void scheduler_run(void)
{
    for (uint8_t i = 0; i < task_num; i++)
    {
        uint32_t now_time = (uint32_t)tick_ms;  // 使用tick_ms作为时基
        // ... 其余逻辑不变
    }
}
```

### 5. 修改 empty.c
**原始问题**: 包含SysTick_Handler()导致重复定义
**解决方案**: 移除SysTick_Handler，使用System文件夹的时间基准

```c
#include "mydefine.h"
#include "System/clock.h"

// 移除了SysTick_Handler()和sys_tick变量声明
// LED闪烁任务实现保持不变
```

## 技术细节

### TIMER_1ms配置
- **定时器**: TIMG0 (TIMER_1ms_INST)
- **时钟源**: BUSCLK (4MHz)
- **分频**: 8分频
- **周期**: 1ms (3999+1个时钟周期)
- **中断**: ZERO事件中断

### 时间基准统一
- **变量**: `volatile unsigned long tick_ms` (System/clock.c)
- **递增**: 每1ms在TIMER_1ms中断中递增
- **用途**: 
  - OLED驱动延时函数 (mspm0_delay_ms, mspm0_get_clock_ms)
  - 任务调度器时间基准

### 兼容性保持
- 保留SysTick_Init()函数名，内部调用Timer_1ms_Init()
- OLED驱动代码无需修改，继续使用mspm0_delay_ms()和mspm0_get_clock_ms()
- 任务调度器功能完全保持，只是时间基准来源改变

## 验证方法
1. 编译项目，确保无错误和警告
2. 观察LED闪烁，验证任务调度器工作正常
3. 测试OLED显示，确保驱动时序正确
4. 使用示波器测量时间精度

## 优势
1. **解决冲突**: 彻底解决SysTick使用冲突
2. **统一时基**: 整个系统使用同一个1ms时间基准
3. **保持兼容**: OLED驱动代码无需修改
4. **资源优化**: 只使用一个定时器资源
5. **优先级明确**: 以调度器需求为准，其他模块适配
