# MSPM0G3507 SysTick定时器配置说明

## 项目概述
本项目基于MSPM0G3507微控制器，实现了一个任务调度器系统，使用SysTick定时器提供1ms的时基。

## 系统配置
- **微控制器**: MSPM0G3507
- **系统时钟**: 32MHz
- **SysTick频率**: 1ms (1000Hz)
- **LED引脚**: GPIOA.14 (PIN_A14)

## SysTick配置详解

### 1. 时钟计算
- 系统时钟频率: 32,000,000 Hz
- 目标中断频率: 1000 Hz (1ms)
- 所需时钟周期数: 32,000,000 / 1000 = 32,000
- SysTick重载值: 32,000 - 1 = 31,999

### 2. 配置代码
```c
// 配置SysTick定时器，每1ms产生一次中断
DL_SYSTICK_config(32000 - 1);

// 启用SysTick中断
DL_SYSTICK_enableInterrupt();

// 启动SysTick定时器
DL_SYSTICK_enable();
```

### 3. 中断服务程序
```c
void SysTick_Handler(void)
{
    sys_tick++;  // 每1ms递增一次
}
```

## 任务调度器实现

### 1. 任务结构
```c
typedef struct {
    void (*task_func)(void);    // 任务函数指针
    uint32_t rate_ms;          // 任务执行周期(ms)
    uint32_t last_run;         // 上次执行时间
} task_t;
```

### 2. LED闪烁任务
```c
// LED闪烁任务，每500ms执行一次
{led_blink_task, 500, 0}
```

### 3. 任务调度逻辑
```c
void scheduler_run(void)
{
    for (uint8_t i = 0; i < task_num; i++)
    {
        uint32_t now_time = sys_tick;
        
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            scheduler_task[i].last_run = now_time;
            scheduler_task[i].task_func();
        }
    }
}
```

## 验证方法

### LED闪烁验证
- LED每500ms切换一次状态
- 闪烁频率: 1Hz (每秒闪烁1次)
- 如果SysTick配置正确，LED应该以稳定的1Hz频率闪烁

### 时间精度验证
可以通过以下方法验证时间精度：
1. 使用示波器测量LED引脚的波形周期
2. 使用逻辑分析仪监测GPIO状态变化
3. 通过串口输出时间戳进行对比

## 文件结构
- `empty.c`: 主程序文件，包含SysTick配置和LED任务
- `scheduler.c`: 任务调度器实现
- `scheduler.h`: 任务调度器头文件
- `mydefine.h`: 公共定义文件
- `ti_msp_dl_config.h/c`: 系统配置文件(自动生成)

## 使用说明
1. 在Code Composer Studio中打开项目
2. 编译项目
3. 下载到MSPM0G3507开发板
4. 观察LED闪烁，验证SysTick工作正常

## 注意事项
1. SysTick中断优先级较高，中断服务程序应尽量简短
2. sys_tick变量为32位，约49.7天后会溢出，需要考虑溢出处理
3. 任务调度器在主循环中运行，不会被中断打断
4. 可以根据需要添加更多任务到scheduler_task数组中
