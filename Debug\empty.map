******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jun 25 21:05:14 2025

OUTPUT FILE NAME:   <empty.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000009b1


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00001348  0001ecb8  R  X
  SRAM                  20200000   00008000  00000221  00007ddf  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001348   00001348    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000a28   00000a28    r-x .text
  00000ae8    00000ae8    00000820   00000820    r-- .rodata
  00001308    00001308    00000040   00000040    r-- .cinit
20200000    20200000    00000021   00000000    rw-
  20200000    20200000    0000001c   00000000    rw- .data
  2020001c    2020001c    00000005   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000a28     
                  000000c0    000002c0     oled_hardware_i2c.o (.text.OLED_Init)
                  00000380    00000134     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  000004b4    000000dc     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  00000590    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  0000062a    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000062c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000006a8    0000007a     oled_hardware_i2c.o (.text.OLED_ShowString)
                  00000722    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00000780    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000007d4    00000044     scheduler.o (.text.scheduler_run)
                  00000818    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000858    00000040     clock.o (.text.SysTick_Init)
                  00000898    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000008d8    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000914    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000948    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  0000097c    00000034     clock.o (.text.mspm0_delay_ms)
                  000009b0    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000009d8    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000009fe    0000001a     empty.o (.text.main)
                  00000a18    00000018     clock.o (.text.mspm0_get_clock_ms)
                  00000a30    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00000a46    00000014     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000a5a    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000a5c    00000014     oled_app.o (.text.oled_task)
                  00000a70    00000014     scheduler.o (.text.scheduler_init)
                  00000a84    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  00000a96    00000002     --HOLE-- [fill = 0]
                  00000a98    00000010     interrupt.o (.text.SysTick_Handler)
                  00000aa8    0000000c     interrupt.o (.text.GROUP1_IRQHandler)
                  00000ab4    0000000c     empty.o (.text.led_blink_task)
                  00000ac0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00000aca    00000002     --HOLE-- [fill = 0]
                  00000acc    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00000ad4    00000006     libc.a : exit.c.obj (.text:abort)
                  00000ada    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00000ade    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00000ae2    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00000ae6    00000002     --HOLE-- [fill = 0]

.cinit     0    00001308    00000040     
                  00001308    00000016     (.cinit..data.load) [load image, compression = lzss]
                  0000131e    00000002     --HOLE-- [fill = 0]
                  00001320    0000000c     (__TI_handler_table)
                  0000132c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00001334    00000010     (__TI_cinit_table)
                  00001344    00000004     --HOLE-- [fill = 0]

.rodata    0    00000ae8    00000820     
                  00000ae8    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  000010d8    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  00001300    00000006     oled_app.o (.rodata.str1.17288060079586621852.1)
                  00001306    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    0000001c     UNINITIALIZED
                  20200000    00000018     scheduler.o (.data.scheduler_task)
                  20200018    00000004     clock.o (.data.sys_tick)

.bss       0    2020001c    00000005     UNINITIALIZED
                  2020001c    00000004     (.common:start_time)
                  20200020    00000001     (.common:task_num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             272    2         0      
       startup_mspm0g350x_ticlang.o   6      192       0      
       scheduler.o                    88     0         25     
       empty.o                        38     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         404    194       25     
                                                              
    .\APP\
       oled_app.o                     20     6         0      
    +--+------------------------------+------+---------+---------+
       Total:                         20     6         0      
                                                              
    .\BSP\
       oled_hardware_i2c.o            1354   2072      0      
    +--+------------------------------+------+---------+---------+
       Total:                         1354   2072      0      
                                                              
    .\System\
       clock.o                        140    0         8      
       interrupt.o                    28     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         168    0         8      
                                                              
    D:/TI/CCS/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_i2c.o                       132    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         142    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         428    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_uidivmod.S.obj           64     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         74     0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      58        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   2594   2330      545    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00001334 records: 2, size/record: 8, table size: 16
	.data: load addr=00001308, load size=00000016 bytes, run addr=20200000, run size=0000001c bytes, compression=lzss
	.bss: load addr=0000132c, load size=00000008 bytes, run addr=2020001c, run size=00000005 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00001320 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
0000062b  ADC0_IRQHandler               
0000062b  ADC1_IRQHandler               
0000062b  AES_IRQHandler                
00000ada  C$$EXIT                       
0000062b  CANFD0_IRQHandler             
0000062b  DAC0_IRQHandler               
00000ac1  DL_Common_delayCycles         
00000723  DL_I2C_fillControllerTXFIFO   
000009d9  DL_I2C_setClockConfig         
0000062b  DMA_IRQHandler                
0000062b  Default_Handler               
0000062b  GROUP0_IRQHandler             
00000aa9  GROUP1_IRQHandler             
00000adb  HOSTexit                      
0000062b  HardFault_Handler             
0000062b  I2C0_IRQHandler               
0000062b  I2C1_IRQHandler               
0000062b  NMI_Handler                   
000000c1  OLED_Init                     
000004b5  OLED_ShowChar                 
000006a9  OLED_ShowString               
00000381  OLED_WR_Byte                  
0000062b  PendSV_Handler                
0000062b  RTC_IRQHandler                
00000adf  Reset_Handler                 
0000062b  SPI0_IRQHandler               
0000062b  SPI1_IRQHandler               
0000062b  SVC_Handler                   
00000915  SYSCFG_DL_GPIO_init           
00000781  SYSCFG_DL_I2C_OLED_init       
00000819  SYSCFG_DL_SYSCTL_init         
00000a47  SYSCFG_DL_init                
00000949  SYSCFG_DL_initPower           
00000a99  SysTick_Handler               
00000859  SysTick_Init                  
0000062b  TIMA0_IRQHandler              
0000062b  TIMA1_IRQHandler              
0000062b  TIMG0_IRQHandler              
0000062b  TIMG12_IRQHandler             
0000062b  TIMG6_IRQHandler              
0000062b  TIMG7_IRQHandler              
0000062b  TIMG8_IRQHandler              
0000062b  UART0_IRQHandler              
0000062b  UART1_IRQHandler              
0000062b  UART2_IRQHandler              
0000062b  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00001334  __TI_CINIT_Base               
00001344  __TI_CINIT_Limit              
00001344  __TI_CINIT_Warm               
00001320  __TI_Handler_Table_Base       
0000132c  __TI_Handler_Table_Limit      
000008d9  __TI_auto_init_nobinit_nopinit
0000062d  __TI_decompress_lzss          
00000a85  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00000a31  __TI_zero_init_nomemset       
00000a5b  __aeabi_idiv0                 
00000acd  __aeabi_memcpy                
00000acd  __aeabi_memcpy4               
00000acd  __aeabi_memcpy8               
00000899  __aeabi_uidiv                 
00000899  __aeabi_uidivmod              
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
000009b1  _c_int00_noargs               
UNDEFED   _system_post_cinit            
00000ae3  _system_pre_init              
00000ad5  abort                         
000010d8  asc2_0806                     
00000ae8  asc2_1608                     
ffffffff  binit                         
00000000  interruptVectors              
00000ab5  led_blink_task                
000009ff  main                          
00000591  memcpy                        
0000097d  mspm0_delay_ms                
00000a19  mspm0_get_clock_ms            
00000a5d  oled_task                     
00000a71  scheduler_init                
000007d5  scheduler_run                 
2020001c  start_time                    
20200018  sys_tick                      
20200020  task_num                      


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  OLED_Init                     
00000200  __STACK_SIZE                  
00000381  OLED_WR_Byte                  
000004b5  OLED_ShowChar                 
00000591  memcpy                        
0000062b  ADC0_IRQHandler               
0000062b  ADC1_IRQHandler               
0000062b  AES_IRQHandler                
0000062b  CANFD0_IRQHandler             
0000062b  DAC0_IRQHandler               
0000062b  DMA_IRQHandler                
0000062b  Default_Handler               
0000062b  GROUP0_IRQHandler             
0000062b  HardFault_Handler             
0000062b  I2C0_IRQHandler               
0000062b  I2C1_IRQHandler               
0000062b  NMI_Handler                   
0000062b  PendSV_Handler                
0000062b  RTC_IRQHandler                
0000062b  SPI0_IRQHandler               
0000062b  SPI1_IRQHandler               
0000062b  SVC_Handler                   
0000062b  TIMA0_IRQHandler              
0000062b  TIMA1_IRQHandler              
0000062b  TIMG0_IRQHandler              
0000062b  TIMG12_IRQHandler             
0000062b  TIMG6_IRQHandler              
0000062b  TIMG7_IRQHandler              
0000062b  TIMG8_IRQHandler              
0000062b  UART0_IRQHandler              
0000062b  UART1_IRQHandler              
0000062b  UART2_IRQHandler              
0000062b  UART3_IRQHandler              
0000062d  __TI_decompress_lzss          
000006a9  OLED_ShowString               
00000723  DL_I2C_fillControllerTXFIFO   
00000781  SYSCFG_DL_I2C_OLED_init       
000007d5  scheduler_run                 
00000819  SYSCFG_DL_SYSCTL_init         
00000859  SysTick_Init                  
00000899  __aeabi_uidiv                 
00000899  __aeabi_uidivmod              
000008d9  __TI_auto_init_nobinit_nopinit
00000915  SYSCFG_DL_GPIO_init           
00000949  SYSCFG_DL_initPower           
0000097d  mspm0_delay_ms                
000009b1  _c_int00_noargs               
000009d9  DL_I2C_setClockConfig         
000009ff  main                          
00000a19  mspm0_get_clock_ms            
00000a31  __TI_zero_init_nomemset       
00000a47  SYSCFG_DL_init                
00000a5b  __aeabi_idiv0                 
00000a5d  oled_task                     
00000a71  scheduler_init                
00000a85  __TI_decompress_none          
00000a99  SysTick_Handler               
00000aa9  GROUP1_IRQHandler             
00000ab5  led_blink_task                
00000ac1  DL_Common_delayCycles         
00000acd  __aeabi_memcpy                
00000acd  __aeabi_memcpy4               
00000acd  __aeabi_memcpy8               
00000ad5  abort                         
00000ada  C$$EXIT                       
00000adb  HOSTexit                      
00000adf  Reset_Handler                 
00000ae3  _system_pre_init              
00000ae8  asc2_1608                     
000010d8  asc2_0806                     
00001320  __TI_Handler_Table_Base       
0000132c  __TI_Handler_Table_Limit      
00001334  __TI_CINIT_Base               
00001344  __TI_CINIT_Limit              
00001344  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200018  sys_tick                      
2020001c  start_time                    
20200020  task_num                      
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[102 symbols]
