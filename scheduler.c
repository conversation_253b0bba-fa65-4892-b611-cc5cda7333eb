#include "scheduler.h"
#include "System/clock.h"

// 外部函数声明
extern void led_blink_task(void);
extern void oled_task(void);

uint8_t task_num;
// 统一的系统时间变量，在System/clock.c中定义
extern volatile uint32_t sys_tick;

typedef struct {
    void (*task_func)(void);
    uint32_t rate_ms;
    uint32_t last_run;
} task_t;

static task_t scheduler_task[] =
{
    {led_blink_task, 500, 0},  // LED闪烁任务，每500ms执行一次
    {oled_task, 5, 0},       // OLED任务，每5ms执行一次
};

void scheduler_init(void)
{
    // 初始化SysTick定时器（统一的1ms时基）
    SysTick_Init();

    // 计算任务数量
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

void scheduler_run(void)
{
    for (uint8_t i = 0; i < task_num; i++)
    {
        uint32_t now_time = sys_tick;  // 使用统一的sys_tick时基

        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            scheduler_task[i].last_run = now_time;
            scheduler_task[i].task_func();
        }
    }
}


